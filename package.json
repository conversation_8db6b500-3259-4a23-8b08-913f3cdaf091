{"name": "yc-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "dev:uat": "vite --mode uat", "dev:prod": "vite --mode prod", "build:dev": "vue-tsc -b && vite build --mode dev", "build:uat": "vue-tsc -b && vite build --mode uat", "build:prod": "vue-tsc -b && vite build --mode prod", "preview": "vite preview"}, "dependencies": {"alova": "^3.3.4", "naive-ui": "^2.42.0", "vue": "3.5.19", "vue-router": "4.5.1"}, "devDependencies": {"@types/node": "^24.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}